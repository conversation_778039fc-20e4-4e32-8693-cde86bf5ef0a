import { LoaderFunction, json } from "@remix-run/node";
import { getItemOptionsAPI } from "~/services/buyer.service";
import { requireAuth } from "~/utils/clientReponse";

export const loader: LoaderFunction = async ({ request }) => {
  // Ensure user is authenticated
  const auth = await requireAuth(request);
  if (auth && auth.authRequired) {
    return json({ success: false, error: "Authentication required" }, { status: 401 });
  }

  try {
    const url = new URL(request.url);
    const sellerId = Number(url.searchParams.get("sellerId")) || undefined;
    const deliveryDate = url.searchParams.get("deliveryDate") || undefined;

    const itemOptions = await getItemOptionsAPI(null, request, deliveryDate, sellerId);

    if(!itemOptions.data) {
      return json({ success: false, error: "No item options found" }, { status: 400 });
    }

    return json({
      success: true,
      itemOptionsData: itemOptions.data
    });
    
  } catch (error) {
    console.error("Error in get-item-options API:", error);
    return json({ success: false, error: "Failed to get item options" }, { status: 500 });
  }
};
