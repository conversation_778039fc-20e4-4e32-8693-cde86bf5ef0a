import DeliveryInfo from "../components/chooseitem/DeliveryInfo";
import React, { useState, useMemo, useEffect, useRef } from "react";
import {
  LoaderFunction,
  json,
  redirect,
  ActionFunction
} from "@remix-run/node";
import {
  useLoaderData,
  useNavigate,
  useFetcher,
  useRouteError,
  isRouteErrorResponse,
  ShouldRevalidateFunction,
  useRevalidator
} from "@remix-run/react";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";

dayjs.extend(customParseFormat);

import MoQPopup from "@components/MoQPopup";
// import RefreshButton from "@components/RefreshButton";
import Button from "@components/Button";

import { getEstimatedDeliveryTime, getItemOptionsAPI, getSellerList, precheckOrderAPI } from "@services/buyer.service";
import {
  getSession,
  destroySession,
  commitSession
} from "@utils/session.server";
import {
  User,
  ItemOptionsData,
  AvailableItem,
  PrecheckOrderPayload,
  PrecheckOrderResponse,
  Cart,
  FulfillmentType,
  Order,
  SellerInfo
} from "~/types";
import { parseJWT } from "~/utils/token-utils";
import { DecodedToken } from "~/types/user";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useUser } from "~/contexts/userContext";
import { removeAllInvalidCarts, removeItem } from "~/utils/localStorage";
import InfoModel from "~/components/models/InfoModel";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { ChevronRight, Clock, MapPin, Store } from "lucide-react";
import { useCartStore } from "~/stores/cart.store";
import RestaurantMenu from "~/components/restaurant/RestaurantMenu";
import { formatCurrency, getEstDeliveryTime } from "~/utils/format";
import { handleWhatsappClick } from "~/components/WhatsappCTA";
import RestaurantDeliveryInfo from "~/components/restaurant/RestaurantDeliveryInfo";
import RestaurantOutletDeliveryInfo from "~/components/restaurant/RestaurantOutletDeliveryInfo";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import { useAnonymousCheck } from "~/hooks/useAnonymousCheck";
import { NetworkAsset } from "~/components/NetworkAssests";
import Toast from "~/components/Toast";
import {
  LOGISTIC_FLOW,
  LOGSTATUS_MESSAGES,
  ORDER_FLOW,
  ORDERSTATUS_MESSAGES
} from "~/components/orders/OrderStatusCard";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { ORDER_POLLING_INTERVAL, ORDER_POLLING_MAX_COUNT } from "~/utils/constant";
import { LoaderData as OrderLoaderData } from "./home.r.order.$orderId";

import InstallPWAButton from "~/components/InstallPWAButton";

import LocationConfirmModal from "~/components/location/LocationConfirmModal";
import { useCurrentLocation } from "~/hooks/useCurrentLocation";
import { useConversionApi } from "~/hooks/useConversionApi";
import BottomSheet from "~/components/BottmSheet";
import { cn } from "~/utils/cn";

interface LoaderData {
  itemOptionsData: ItemOptionsData;
  sellerList: SellerInfo[];
  estDeliveryTime: number;
  user: User;
  mobileNumber: string;
}

interface LoaderErrorData {
  error: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  let session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  console.log("Loader called for chooseitems.tsx");

  const url = new URL(request.url);
  let deliveryDate; // string | undefined;
  let sellerId;     // string | null;
  deliveryDate = url.searchParams.get("deliveryDate") || undefined;
  sellerId = url.searchParams.get("sellerId");
  const redirectedFromBuyer =
    (url.searchParams.get("redirected") as unknown as boolean) || false;

  const categoryId = url.searchParams.get("categoryId");
  const matchBy = url.searchParams.get("matchBy")?.trim();
  const parentCategoryId = url.searchParams.get("parentCategoryId");

  // console.log("DeliveryDate:", deliveryDate, sellerId, categoryId);

  if (!access_token || !user) {
    const headers = new Headers();
    headers.append("Set-Cookie", await destroySession(session));
    session = await getSession();
    session.set("appConfig", { appStartRoute: url.pathname });
    headers.append("Set-Cookie", await commitSession(session));
    return redirect(`/login?redirectTo=${request.url}`, { headers });
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      const headers = new Headers();
      headers.append("Set-Cookie", await destroySession(session));
      session = await getSession();
      session.set("appConfig", { appStartRoute: url.pathname });
      headers.append("Set-Cookie", await commitSession(session));
      return redirect(`/login?redirectTo=${request.url}`, { headers });
    }

    const headers = new Headers();

    // if multi seller is true then get device location
    let lat = undefined;
    let long = undefined;
    try {
      const multiSeller = request.headers.get("Cookie")?.split(";").find((cookie) => cookie.trim().startsWith("multiSeller="));
      if (multiSeller && multiSeller.split("=")[1] === "true") {
        try {
          const coordinates = request.headers.get("Cookie")?.split(";").find((cookie) => cookie.trim().startsWith("coordinates="));
          if (coordinates) {
            const decodedCoordinates = decodeURIComponent(coordinates.split("=")[1]);
            const { latitude, longitude } = JSON.parse(decodedCoordinates);
            lat = latitude;
            long = longitude;
          }

          if (!lat || !long) {
            return json({ error: "LOCATION_ACCESS_REQUIRED" });
          }
        } catch (e) {
          console.log("Error parsing coordinates:", e);
          return json({ error: "LOCATION_ACCESS_REQUIRED" });
        }
        // get and set selectedSeller cookie
        try {
          if (!sellerId && !deliveryDate) {
            const selectedSellerCookie = request.headers.get("Cookie")?.split(";").find((cookie) => cookie.trim().startsWith("selectedSeller="));
            if (selectedSellerCookie) {
              const decodedSeller = decodeURIComponent(selectedSellerCookie.split("=")[1]);
              const { sellerId: selectedSellerId, deliveryDate: selectedDeliveryDate } = JSON.parse(decodedSeller);
              sellerId = selectedSellerId;
              deliveryDate = selectedDeliveryDate;
            }
          }
          if (sellerId && deliveryDate) {
            const selectedSeller = JSON.stringify({ sellerId, deliveryDate });
            headers.set("Set-Cookie", `selectedSeller=${encodeURIComponent(selectedSeller)}; path=/; max-age=86400`);
          }
        } catch (e) {
          console.log("Error parsing selected seller")
        }
      }
    } catch (error) {
      console.error("Error getting network config:", error);
    }

    const itemOptions = await getItemOptionsAPI(
      redirectedFromBuyer ? decoded.userDetails.mobileNumber : null,
      request,
      deliveryDate,
      Number(sellerId),
      categoryId ? Number(categoryId) : undefined,
      matchBy ?? undefined,
      parentCategoryId ? Number(parentCategoryId) : undefined,
      lat,
      long
    );

    const itemOptionsData = itemOptions.data;

    if (!itemOptionsData) {
      throw json({}, { status: 400, statusText: "NO_ITEM" });
    }

    // if multi seller is true then get seller list
    let sellerList: SellerInfo[] = [];
    let estDeliveryTime = 0;
    if (lat && long) {
      if (deliveryDate) {
        try {
          const sellerListResponse = await getSellerList(lat, long, deliveryDate, request);
          sellerList = sellerListResponse.data;
        } catch (error) {
          console.log("Error fetching seller list");
        }
      }
      if (sellerId) {
        // get estimated delivery time
        try {
          const estDeliveryTimeResponse = await getEstimatedDeliveryTime(lat, long, Number(sellerId), request);
          estDeliveryTime = estDeliveryTimeResponse.data.data.estimated_delivery_time;
        } catch (error) {
          console.log("Error fetching est delivery time");
        }
      }
    }

    return createClientResponse<LoaderData, ItemOptionsData>(
      request,
      {
        itemOptionsData,
        sellerList,
        estDeliveryTime,
        user,
        mobileNumber: decoded.userDetails.mobileNumber
      },
      {
        ...itemOptions,
        headers
      }
    );
  } catch (error) {
    console.error("Error fetching item options:", error);
    if (error instanceof Response && error.statusText === "NO_ITEM") {
      throw json({}, { status: 400, statusText: "NO_ITEM" });
    }
    return json({}, { status: 400, statusText: "Items are not available" });
  }
};

// Updated Action Function to handle CONFIRM button submission via Fetcher
export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;
  // const url = new URL(request.url);

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token || !user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const cartData = formData.get("cart");
  const deliveryDate = formData.get("deliveryDate") as string;
  const sellerId = formData.get("sellerId") as string;
  const codAllowed = formData.get("codAllowed") === "true";
  const sellerDataId = formData.get("sellerDataId") as string;
  const existingOrderGroupId =
    (formData.get("existingOrderGroupId") !== null &&
      Number(formData.get("existingOrderGroupId"))) ||
    undefined;
  const sellerMessage = formData.get("sellerMessage") as string;
  const cartKey = formData.get("cartKey") as string;
  const preconfirmUid = formData.get("preconfirmUid") as string;
  const couponId = formData.get("couponId")
    ? Number(formData.get("couponId"))
    : undefined;
  const fulfillmentType = formData.get("fulfillmentType") as FulfillmentType;
  const takeAwayEnabled = formData.get("takeAwayEnabled") === "true";

  if (!cartData || typeof cartData !== "string") {
    return json({ error: "Invalid cart data" }, { status: 400 });
  }

  let cart: Cart;
  try {
    cart = JSON.parse(cartData);
  } catch (error) {
    console.error("Error parsing cart data:", error);
    return json({ error: "Invalid cart data format" }, { status: 400 });
  }

  // Prepare items array for API
  const itemsForAPI: PrecheckOrderPayload["items"] = Object.values(cart).map(
    (cartItem) => ({
      inventoryId: Number(sellerDataId),
      sellerId: Number(sellerId),
      sellerItemId: cartItem.itemId,
      pricePerUnit: cartItem.amount / cartItem.qty,
      quantity: cartItem.qty,
      variationId: cartItem.variationId,
      addOnItems: cartItem.flatAddons
        ? cartItem.flatAddons.map((addon) => ({
          id: addon.id,
          sId: addon.sId,
          name: addon.name,
          price: addon.price,
          qty: addon.qty,
          seq: addon.seq || 0,
          diet: addon.diet || null
        }))
        : []
    })
  );

  // Prepare payload
  const payload: PrecheckOrderPayload = {
    sellerInventoryId: Number(sellerDataId),
    buyerId: user.buyerId,
    deliveryDate,
    sellerId: Number(sellerId),
    codOpted: codAllowed,
    items: itemsForAPI,
    legacy: true,
    moneyCollectionId: 0,
    existingOrderGroupId,
    cartKey
  };

  if (preconfirmUid) {
    payload.preconfirmUid = preconfirmUid;
  }

  if (sellerMessage) {
    payload.sellerMessage = sellerMessage;
  }

  if (couponId) {
    payload.couponId = couponId;
  }

  if (fulfillmentType) {
    payload.fulfillmentType = fulfillmentType;
  } else if (takeAwayEnabled) {
    payload.fulfillmentType = "TAKE_AWAY";
  } else {
    payload.fulfillmentType = "DELIVERY";
  }

  try {
    // console.log("Prechecking order with payload:", JSON.stringify(payload));
    const response = await precheckOrderAPI(payload, request);
    // console.log("Precheck Order Response:", JSON.stringify(response));

    // Return the order data as JSON
    return createClientResponse<PrecheckOrderResponse, PrecheckOrderResponse>(
      request,
      response.data,
      response
    );
  } catch (error) {
    console.error("Error prechecking order:", error);
    return json(
      { error: "Something went wrong, Please try again." },
      { status: 500 }
    );
  }
};

export const shouldRevalidate: ShouldRevalidateFunction = ({
  actionResult,
  defaultShouldRevalidate
}) => {
  // console.log("actionResult", actionResult);
  if (actionResult && actionResult.buyerId) {
    console.log("actionResult is not null");
    return false;
  }
  return defaultShouldRevalidate;
};

const ChooseItems: React.FC = () => {
  const navigate = useNavigate();
  const loader = useLoaderData<LoaderData & LoaderErrorData>();
  const { error } = loader;
  const itemFetcher = useFetcher<LoaderData & LoaderErrorData>();

  const { revalidate } = useRevalidator()

  const { authRequired } = useRequireAuth();
  const { requireRealAuth } = useAnonymousCheck();

  const {
    itemOptionsData,
    setItemOptionsData,
    searchPage,
    categoryType,
    setCategoryType,
    setLoading,
    selectedParentCategory,
    categoryTypeList,
    setIsScrolled,
    imageViewType,
    setImageViewType
  } = chooseitemsStore((state) => state);

  const {
    cart,
    addItem: addToCart,
    removeItem: removeFromCart,
    syncCart,
    clearCart,
    totalAmount: totalCartAmount,
    itemCount: totalCartItemCount
  } = useCartStore((state) => state);

  const [sellerList, setSellerList] = useState<SellerInfo[]>([]);
  const [estDeliveryTime, setEstDeliveryTime] = useState<number>(0);
  const { trackAddToCart } = useConversionApi();

  useEffect(() => {
    if (loader.sellerList && loader.sellerList.length > 0) {
      setSellerList(loader.sellerList);
    }
    if (itemFetcher.data?.sellerList && itemFetcher.data?.sellerList.length > 0) {
      setSellerList(itemFetcher.data.sellerList);
    }
  }, [loader.sellerList, itemFetcher.data?.sellerList]);
  
  useEffect(() => {
    if (loader.estDeliveryTime) {
      setEstDeliveryTime(loader.estDeliveryTime);
    }
    if (itemFetcher.data?.estDeliveryTime) {
      setEstDeliveryTime(itemFetcher.data.estDeliveryTime);
    }
  }, [loader.estDeliveryTime, itemFetcher.data?.estDeliveryTime]);

  // Ensure cart is initialized only when cartKey changes
  useEffect(() => {
    if (
      loader.itemOptionsData?.availableItems &&
      loader.itemOptionsData?.cartKey
    ) {
      syncCart(
        loader.itemOptionsData.availableItems as AvailableItem[],
        loader.itemOptionsData.cartKey
      );
      removeItem("currentOrder");
    }
  }, [loader.itemOptionsData?.cartKey]); // Only depend on cartKey changes

  useEffect(() => {
    setItemOptionsData(loader.itemOptionsData as ItemOptionsData);
    removeItem("imageViewType");

    if (itemOptionsData?.cartKey) {
      removeAllInvalidCarts(itemOptionsData?.cartKey);
    }
    if (!imageViewType) {
      setImageViewType(
        loader?.itemOptionsData?.networkType === "B2B" ? "LIST" : "GRID"
      );
    }
  }, [loader, setItemOptionsData]);

  useEffect(() => {
    if (itemFetcher.data) {
      setItemOptionsData({
        ...itemFetcher?.data.itemOptionsData
      } as ItemOptionsData);
      if (itemFetcher?.data?.itemOptionsData?.cartKey) {
        syncCart(
          itemFetcher?.data?.itemOptionsData?.availableItems as AvailableItem[],
          itemFetcher?.data?.itemOptionsData?.cartKey
        );
      }
      setLoading(false);
      if (itemOptionsData?.cartKey) {
        removeAllInvalidCarts(itemOptionsData?.cartKey);
        removeItem("currentOrder");
      }
    }
  }, [itemFetcher.state, itemFetcher.data, setItemOptionsData, setLoading]);

  // State variables
  const [showMoq, setShowMoq] = useState(false);
  const [showMov, setShowMov] = useState(false);
  const [showBackConfirmation, setShowBackConfirmation] = useState(false);
  const { user, setUser } = useUser();
  const [searchStr, setSearchStr] = useState("");
  const [showOutletSheet, setShowOutletSheet] = useState(false);

  // The entire page container
  const pageContainerRef = useRef<HTMLDivElement>(null);

  // The sentinel for detecting scroll position
  const searchBarSentinelRef = useRef<HTMLDivElement>(null);
  const itemSRPSentinelRef = useRef<HTMLDivElement>(null);

  // We no longer need to track isSearchSticky as we're using a fixed header approach
  useEffect(() => {
    const sentinel = searchBarSentinelRef.current;
    if (!sentinel || !pageContainerRef.current) return;

    // Keep the observer for future use if needed
    const observer = new IntersectionObserver(
      () => {
        // No op - we're not using this value anymore
      },
      {
        threshold: 0,
        root: pageContainerRef.current,
        rootMargin: `-8px`
      }
    );

    observer.observe(sentinel);
    return () => observer.disconnect();
  }, [searchBarSentinelRef.current]);

  /** Observer for Item List */
  useEffect(() => {
    const sentinel = itemSRPSentinelRef.current;
    if (!sentinel || !pageContainerRef.current) return;

    const observer = new IntersectionObserver(
      () => {
        // No longer using this intersection observation result
      },
      {
        threshold: 0,
        root: pageContainerRef.current,
        rootMargin: `-72px`
      }
    );

    observer.observe(sentinel);
    return () => observer.disconnect();
  }, [itemSRPSentinelRef.current]);

  const { appSource, networkConfig } = useAppConfigStore((state) => state);
  useEffect(() => {
    if (itemOptionsData?.existingOrderGroupId) {
      setUser({ existingOrderGroupId: itemOptionsData?.existingOrderGroupId });
    }
  }, [itemOptionsData?.existingOrderGroupId, setUser]);

  const itemMap = new Map<number, AvailableItem>();
  itemOptionsData?.availableItems?.forEach((item) => {
    itemMap.set(item.sellerItemId, item);
  });

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fetcher = useFetcher<PrecheckOrderResponse | { error: string }>();
  const [showToast, setShowToast] = useState(false);
  const [showErrorToast, setShowErrorToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState<
    "success" | "error" | "warning" | "info" | "itemLimited"
  >("itemLimited");
  // Handle the fetcher response
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if ("error" in fetcher.data) {
        setErrorMessage(fetcher.data.error);
        setShowErrorToast(true);
      } else {
        // Navigate to /cart
        navigate(`/r/cart?approxPricing=${itemOptionsData?.approxPricing}`, {
          state: {
            order: fetcher.data
          }
        });
      }
    }
  }, [fetcher.state, fetcher.data, navigate, itemOptionsData?.approxPricing]);

  const handleBack = () => {
    setIsScrolled(false);

    if (searchStr.length > 0) {
      handleSearchClear();
      return;
    }

    // if (appSource === "whatsappchat") {
    //   setShowBackConfirmation(true);
    //   return;
    // }

    performBackNavigation(false);
  };

  const performBackNavigation = (goToWA: boolean = false) => {
    if (
      categoryType === "L1" &&
      (categoryTypeList.includes("L2") || categoryTypeList.includes("L3"))
    ) {
      navigate("/home/<USER>");
    } else if (appSource === "whatsappchat") {
      if (goToWA) {
        handleWhatsappClick(networkConfig?.wabMobileNumber || "");
      } else {
        navigate("/home/<USER>");
      }
      console.log("whatsapp back nav called");
    } else if (networkConfig?.multiSeller && itemOptionsData?.deliveryDate) {
      navigate(
        `/selectseller?deliveryDate=${encodeURIComponent(
          itemOptionsData?.deliveryDate
        )}`
      );
    } else {
      navigate("/home");
    }
  };

  const handleSelectL1 = (categoryId: number) => {
    setCategoryType("L1");
    setLoading(true);
    if (categoryType !== "L1") {
      itemFetcher.submit(
        {
          intent: "L1",
          categoryId,
          deliveryDate: itemOptionsData?.deliveryDate || null,
          sellerId: itemOptionsData?.sellerId || null,
          parentCategoryId: selectedParentCategory?.id || null
        },
        {
          method: "GET",
          action: `/home/<USER>
        }
      );
    }
  };

  const getButtonText = () => {
    if (categoryType === "L1" && selectedParentCategory?.name) {
      return selectedParentCategory.name;
    } else {
      return "Add Items";
    }
  };

  const handleSearchClear = () => {
    if (searchPage === "L1") {
      itemFetcher.submit(
        {
          intent: "L1",
          deliveryDate: itemOptionsData?.deliveryDate || null,
          sellerId: itemOptionsData?.sellerId || null,
          categoryId: selectedParentCategory?.id ?? null
        },
        {
          method: "GET",
          action: `/home/<USER>
        }
      );
    } else {
      itemFetcher.submit(
        {
          intent: "L1",
          deliveryDate: itemOptionsData?.deliveryDate || null,
          sellerId: itemOptionsData?.sellerId || null
        },
        {
          method: "GET",
          action: `/home/<USER>
        }
      );
    }
    setSearchStr("");
  };

  const showConfirmButton = useMemo(() => {
    return (
      Object.values(cart).length > 0 &&
      itemOptionsData &&
      itemOptionsData?.availableItems &&
      itemOptionsData?.availableItems?.length > 0
    );
  }, [cart, itemOptionsData]);

  const [ongoingOrder, setOngoingOrder] = useState<Order | null>(loader.itemOptionsData?.ongoingOrder as Order | null);
  const [showOrderStatus, setShowOrderStatus] = useState<boolean>(!!loader.itemOptionsData?.ongoingOrder && loader.itemOptionsData.ongoingOrder.fulfillmentType === "DELIVERY");
  const orderFetcher = useFetcher<OrderLoaderData>();
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const pollCountRef = useRef(0);

  // Add polling effect for order updates
  useEffect(() => {
    // Only poll if order is in an active status
    const shouldPoll = showOrderStatus && ongoingOrder && ongoingOrder.status !== "Delivered" && ongoingOrder.status !== "Cancelled";

    // Clear any existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Set up polling if needed
    if (shouldPoll) {
      pollingIntervalRef.current = setInterval(() => {
        if (pollCountRef.current >= ORDER_POLLING_MAX_COUNT) {
          clearInterval(pollingIntervalRef.current!);
          return;
        }
        pollCountRef.current += 1;
        orderFetcher.load(`/home/<USER>/order/${ongoingOrder.id}`);
      }, ORDER_POLLING_INTERVAL);
    }

    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [ongoingOrder?.id, ongoingOrder?.status]);


  // Update order state when fetcher returns new data
  useEffect(() => {
    if (orderFetcher.data?.order) {
      const updatedOrder = orderFetcher.data.order as Order;
      if (updatedOrder.status !== "Delivered" && updatedOrder.status !== "Cancelled" || updatedOrder.fulfillmentType === "DELIVERY") {
        setOngoingOrder(updatedOrder);
        setShowOrderStatus(true);
      }
      else {
        setOngoingOrder(null);
        setShowOrderStatus(false);
      }
    }
  }, [orderFetcher.data]);

  const handleRefresh = async () => {
    if (itemOptionsData?.cartKey) {
      clearCart(itemOptionsData.cartKey);
    }
    setIsRefreshing(true);
    try {
      navigate(0);
    } catch (err) {
      console.error("Error refreshing item options:", err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleOutletSelection = (seller: SellerInfo) => {
    // Clear current cart when switching outlets
    if (itemOptionsData?.cartKey) {
      clearCart(itemOptionsData.cartKey);
    }

    // Fetch items for the selected seller
    itemFetcher.submit(
      {
        deliveryDate: itemOptionsData?.deliveryDate || "",
        sellerId: seller.id.toString()
      },
      {
        method: "GET",
        action: `/home/<USER>
      }
    );
  };

  const handleAddItem = (item: AvailableItem) => {
    if (itemOptionsData?.cartKey) {
      const currentQty = cart[item.sellerItemId]?.qty || 0;
      const incrementOrderQty = item.incrementOrderQty || 1;
      if (!itemOptionsData.cartKey) return;

      if (currentQty + incrementOrderQty > item.maxAvailableQty) {
        setToastMessage(
          `Sorry, we have limited quantity available for this item!`
          // `Cannot add more than ${item.availableCartItem.maxAvailableQty} units of ${item.itemName}`
        );
        setToastType("itemLimited");
        setShowToast(true);
        return;
      }

      addToCart(item, itemOptionsData.cartKey);
    }
  };

  const handleRemoveItem = (item: AvailableItem) => {
    if (itemOptionsData?.cartKey) {
      const currentQty = cart[item.sellerItemId]?.qty || 0;
      const orderedQty = item.orderedQty || 0;

      if (!itemOptionsData.cartKey) return;

      if (currentQty <= orderedQty) {
        setToastMessage(
          `Cannot reduce below ordered quantity of ${orderedQty} units`
        );
        setToastType("warning");
        setShowToast(true);
        return;
      }

      removeFromCart(item, itemOptionsData.cartKey);
    }
  };

  const handleMinQuantityAndPrice = () => {
    const minQty: boolean =
      Object.values(cart).reduce((acc, item) => {
        const itemDetails = itemMap.get(item.itemId);
        if (itemDetails && itemDetails.unitWtFactor) {
          return acc + item.qty * itemDetails.unitWtFactor;
        }
        return acc + item.qty;
      }, 0) < itemOptionsData!.minOrderQty;
    const minPrice: boolean =
      Object.values(cart).reduce((acc, item) => acc + item.amount, 0) <
      itemOptionsData!.minOrderValue;

    if (minQty) {
      setShowMoq(true);
      return true;
    }
    if (minPrice) {
      setShowMov(true);
      return true;
    }

    return minQty || minPrice;
  };

  // Add event listener for browser back button
  useEffect(() => {
    // Flag to track if this is the initial page load
    let isInitialLoad = true;

    // Handle the browser's back button press
    const handlePopState = () => {
      // Skip on initial page load to avoid false positives
      if (isInitialLoad) {
        isInitialLoad = false;
        return;
      }

      if (appSource === "whatsappchat") {
        // Push state again to prevent actual navigation
        window.history.pushState(
          { page: "chooseitems" },
          document.title,
          window.location.href
        );

        // Show confirmation dialog
        setShowBackConfirmation(true);
      }
    };

    // Add the event listener for popstate (browser back button)
    window.addEventListener("popstate", handlePopState);

    // Push a state into the history so popstate can be triggered when back is pressed
    setTimeout(() => {
      window.history.pushState(
        { page: "chooseitems" },
        document.title,
        window.location.href
      );
      isInitialLoad = false;
    }, 0);

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [appSource]);

  if (authRequired) {
    return (
      <div className="flex items-center justify-center h-screen">
        <NetworkAsset assetName="banner" />
      </div>
    );
  }

  const { isRequesting, request, refresh, error: locationError } = useCurrentLocation();
  const [showLocationModal, setShowLocationModal] = useState(false);

  useEffect(() => {
    if (error === "LOCATION_ACCESS_REQUIRED" && !showLocationModal) {
      refresh(
        (position) => {
          revalidate();
        },
        (error) => {
          setShowLocationModal(true);
        }
      );
    }
  }, [error, showLocationModal]);

  // get location
  if (error === "LOCATION_ACCESS_REQUIRED") {
    // show loader to retry when permission is in granted state
    if (!showLocationModal) {
      return (
        <SpinnerLoader loading={true} size={12} />
      );
    }

    return (
      <div>
        <LocationConfirmModal
          title={locationError?.PERMISSION_DENIED ? "Location Access Denied" : locationError?.POSITION_UNAVAILABLE ? "Unable to detect your location" : "We need your location to continue"}
          address={""}
          message={""}
          onUpdateLocation={() => request(() => revalidate())}
          buttonText={isRequesting ? "Loading..." : locationError?.PERMISSION_DENIED ? "Enable in Browser Settings" : locationError?.POSITION_UNAVAILABLE ? "Retry" : "Allow Location"}
        />
      </div>
    );
  }

  if (itemOptionsData?.avStatus !== "ok") {
    return itemOptionsData?.avStatus === "bookingClosed" ? (
      <InfoModel
        title="Sorry, we're closed!"
        message="We're currently not accepting orders online. We'll be back soon."
        buttonType="secondary"
        buttonText="Go Back"
        specialCase="BookingClosed"
        onClose={handleBack}
        isCountdownRedirectionAllowed={false}
      />
    ) : itemOptionsData?.avStatus === "notInServiceArea" ? (
      <InfoModel
        title="Sorry, we're not there yet!"
        message="We're not available at your location at the moment. Please try a different location."
        buttonType="primary"
        buttonText="Try Changing Location"
        specialCase="OutofServiceArea"
        onClose={() => navigate("/changeaddress")}
        isCountdownRedirectionAllowed={false}
      />
    ) : (
      <InfoModel
        title="Sorry!"
        message="Something went wrong. We will be back soon."
        buttonType="primary"
        buttonText="Go Back"
        onRedirect={handleBack}
        countdownStart={60}
      />
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-[#fcfcfd]">
        <span className="text-red-500 text-lg">{error}</span>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col h-screen w-full bg-neutral-50 no-scrollbar overflow-hidden`}
    >
      {(fetcher.state !== "idle" || itemFetcher.state !== "idle") && (
        <SpinnerLoader loading={true} size={12} />
      )}
      {/* Back Confirmation Modal */}
      {showBackConfirmation && (
        <InfoModel
          title="Leave this page?"
          message="Are you sure you want to leave this page? Your progress will not be saved."
          buttonType="primary"
          buttonText="Stay on Page"
          onClose={() => {
            setShowBackConfirmation(false);
          }}
          onRedirect={() => {
            setShowBackConfirmation(false);
            performBackNavigation(true);
          }}
          countdownStart={5}
          isCountdownRedirectionAllowed={false}
          specialCase="BackNavigation"
        />
      )}
      {/* Fixed Header Section */}
      {/* <div className="sticky top-0 z-40">
        <BackNavHeader
          backButton={true}
          buttonText={getButtonText()}
          handleBack={handleBack}
          className={`bg-primary text-white shadow-none`}
          pageName="SRP"
          rightText={
            <RefreshButton onClick={handleRefresh} loading={isRefreshing} />
          }
        />
      </div> */}

      {/* Main scrollable container */}
      <div
        ref={pageContainerRef}
        className="flex-1 overflow-y-auto h-full w-full relative no-scrollbar"
      >
        <div className="transition-all duration-500">
          <InstallPWAButton
            themeColor={"non-primary"}
            title="Get The App"
            subtitle="For Better Experience"
          />
        </div>
        {/* Delivery Info (scrolls away) */}
        <div className="bg-primary text-white">
          {
            networkConfig?.multiSeller && networkConfig?.ondcDomain === "RET11" ? (
              <RestaurantOutletDeliveryInfo
                sellerList={sellerList}
                estDeliveryTime={estDeliveryTime}
                defaultAddress={itemOptionsData?.defaultAddress}
                onSellerClick={() => {
                  setShowOutletSheet(true);
                }}
                onAddressClick={() => {
                  if (requireRealAuth()) {
                    return;
                  }
                  navigate(
                    "/select-address?flowType=select-address&returnTo=/home/<USER>",
                    {
                      state: {
                        from: "/home/<USER>",
                        returnTo: `/home/<USER>
                        flowType: "select-address"
                      }
                    }
                  )
                }}
                onProfileClick={() => {
                  if (requireRealAuth()) {
                    return;
                  }
                  navigate("/home/<USER>")
                }}
              />
            ) : (
              <RestaurantDeliveryInfo
                defaultAddress={itemOptionsData?.defaultAddress}
                onAddressClick={() => {
                  if (requireRealAuth()) {
                    return;
                  }
                  navigate(
                    "/select-address?flowType=select-address&returnTo=/home/<USER>",
                    {
                      state: {
                        from: "/home/<USER>",
                        returnTo: "/home/<USER>",
                        flowType: "select-address"
                      }
                    }
                  )
                }}
                onProfileClick={() => {
                  if (requireRealAuth()) {
                    return;
                  }
                  navigate("/home/<USER>")
                }}
              />
            )}
        </div>

        {/* Error message from order confirmation */}
        {/* {errorMessage && (
          <div className="p-4 bg-red-100 text-red-700 text-center">
            {errorMessage}
          </div>
        )} */}

        {/* Banner - This should scroll away */}
        {/* {itemOptionsData?.sellerBanners?.length > 0 && (
          <div>
            <Banners
              images={itemOptionsData.sellerBanners}
              className="px-4 py-2 bg-gray-100 shadow-lg"
            />
          </div>
        )} */}

        {/* Restaurant Menu with sticky search */}
        <div className="h-full flex-grow">
          <RestaurantMenu
            cart={cart}
            data={{ ...itemOptionsData }}
            onAddItem={handleAddItem}
            onRemoveItem={handleRemoveItem}
            searchStr={searchStr}
            imageViewType={"R-VIEW"}
            handleSelectL1={handleSelectL1}
            menuButtonPosition={
              showConfirmButton && showOrderStatus
                ? "9.5rem"
                : showConfirmButton || showOrderStatus
                  ? "5rem"
                  : "1rem"
            }
          />
        </div>
      </div>

      <div className="fixed inset-0 bg-transparent pointer-events-none z-30">
        <div className="fixed bottom-0 left-0 right-0 flex flex-col justify-end items-center">
          {/* Floating action button for cart */}
          {showConfirmButton && (
            <div className="w-full mb-2">
              <fetcher.Form
                method="post"
                className="p-0 flex flex-col justify-center items-center"
                onSubmit={(event) => {
                  if (handleMinQuantityAndPrice()) {
                    event.preventDefault();
                  }
                  else {
                    trackAddToCart({
                      eventData: {
                        contentType: "product",
                        contentIds: Object.keys(cart),
                        contentName: itemOptionsData?.sellerName,
                        contentCategory: "Food",
                        currency: "INR",
                        value: totalCartAmount,
                        numItems: totalCartItemCount
                      },
                      eventId: `EVT-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
                      eventTime: Date.now()
                    });
                  }
                }}
              >
                {/* item total and total items */}
                <Button className="pointer-events-auto flex self-center items-center w-[80%] justify-between rounded-2xl shadow-2xl bg-primary p-3">
                  <div className="flex flex-col items-start">
                    <div className="flex gap-1">
                      <span className="text-xs text-white">
                        {`${totalCartItemCount} 
                    ${totalCartItemCount <= 1 ? "Item" : "Items"}`}
                      </span>
                    </div>
                    <span className="text-sm text-white">
                      {formatCurrency(totalCartAmount)}
                    </span>
                  </div>
                  {/* Hidden Inputs to Pass Necessary Data */}
                  <input
                    type="hidden"
                    name="cart"
                    value={JSON.stringify(cart)}
                  />
                  <input
                    type="hidden"
                    name="deliveryDate"
                    value={itemOptionsData?.deliveryDate}
                  />
                  <input
                    type="hidden"
                    name="sellerId"
                    value={itemOptionsData?.sellerId}
                  />
                  <input
                    type="hidden"
                    name="sellerDataId"
                    value={itemOptionsData?.inventoryId}
                  />
                  <input
                    type="hidden"
                    name="codAllowed"
                    value={itemOptionsData?.codAllowed ? "true" : "false"}
                  />
                  <input
                    type="hidden"
                    name="existingOrderGroupId"
                    value={user?.existingOrderGroupId}
                  />
                  <input
                    type="hidden"
                    name="cartKey"
                    value={itemOptionsData?.cartKey}
                  />
                  <input
                    type="hidden"
                    name="takeAwayEnabled"
                    value={itemOptionsData?.takeAwayEnabled ? "true" : "false"}
                  />

                  {/* confirm btn */}
                  <div
                    className={`flex text-white ${Object.values(cart).length === 0
                      ? "cursor-not-allowed"
                      : " text-white"
                      }`}
                  >
                    {fetcher.state === "submitting" ? "PROCEED..." : "PROCEED"}
                    <ChevronRight />
                  </div>
                </Button>
              </fetcher.Form>
            </div>
          )}

          {showOrderStatus && (
            <div
              className="pointer-events-auto w-full bg-white rounded-t-lg p-3 flex flex-row items-center justify-between gap-2 shadow-[0px_2px_16px_0px_rgba(0,0,0,0.1)]"
              onClick={() => navigate(`/home/<USER>/order/${ongoingOrder?.id}`)}
            >
              <div>
                <div className="px-3 py-2 rounded-lg flex flex-col items-center justify-center text-white bg-gradient-to-b from-[#00A38F] to-[#007661]">
                  <p className="text-xl font-bold leading-5">
                    {getEstDeliveryTime(ongoingOrder?.estDeliveryTime || "")}
                  </p>
                  <p className="text-xs leading-3">mins</p>
                </div>
              </div>
              <div className="flex flex-col items-start justify-start flex-1">
                {/* order status */}
                <p className="text-lg tracking-wide font-bold leading-[1.5rem] line-clamp-1">
                  {ORDER_FLOW.indexOf(ongoingOrder?.status || "Created") >=
                    ORDER_FLOW.indexOf("PickedUp") &&
                    LOGISTIC_FLOW.indexOf(
                      ongoingOrder?.logStatus || "LOG_CREATED"
                    ) >= LOGISTIC_FLOW.indexOf("LOG_REACHED_LOCATION")
                    ? "Arrived at location"
                    : ORDERSTATUS_MESSAGES[ongoingOrder?.status || "Created"] ||
                    "Order recieved!"}
                </p>

                {/* logistic status */}
                {LOGISTIC_FLOW.indexOf(
                  ongoingOrder?.logStatus || "LOG_CREATED"
                ) < LOGISTIC_FLOW.indexOf("LOG_AGENT_ASSIGNED") ? (
                  <p className="text-xs text-typography-400 line-clamp-2">
                    Looking for a delivery partner.
                  </p>
                ) : (
                  <p className="text-xs text-typography-400 line-clamp-2">
                    <span className="text-typography-600">
                      {ongoingOrder?.delPartnerName || "Delivery Partner"}
                    </span>
                    <span>
                      &nbsp;
                      {LOGSTATUS_MESSAGES[
                        ongoingOrder?.logStatus || "LOG_AGENT_ASSIGNED"
                      ].toLowerCase()}
                    </span>
                  </p>
                )}
              </div>
              <div>
                <Button
                  onClick={() => navigate(`/home/<USER>/order/${ongoingOrder?.id}`)}
                  className="bg-primary text-white p-2 rounded-xl flex justify-between align-center cursor-pointer"
                >
                  <ChevronRight className="self-center" size={"1.25rem"} />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Outlet Selection UI */}
      <BottomSheet
        isOpen={showOutletSheet}
        onClose={() => setShowOutletSheet(false)}
        className="bg-white"
        showSwipeIndicator={true}
        sheetType="drawer"
        showCloseButton={false}
      >
        <div className="p-2">
          {/* Header */}
          <div className="pb-2 mb-2 border-b border-gray-100">
            <h2 className="text-xl font-bold text-gray-900">
              Choose Outlet
            </h2>
            <p className="text-sm text-gray-500">
              Select from {sellerList.length} available outlet{sellerList.length !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Outlet List */}
          <div className="space-y-2 max-h-[65vh] overflow-y-auto no-scrollbar pb-3">
            {sellerList.map((seller) => (
              <div
                key={seller.id}
                className={cn(
                  "relative rounded-xl p-3 cursor-pointer transition-all duration-300 border-2",
                  seller.id === itemOptionsData?.sellerId
                    ? "border-primary bg-gradient-to-r from-primary/5 to-primary/10"
                    : "border-gray-200 bg-white hover:border-primary/30 hover:shadow-md hover:bg-gray-50/50"
                )}
                onClick={() => {
                  setShowOutletSheet(false);
                  handleOutletSelection(seller)
                }}
              >
                <div className="flex items-start gap-3">
                  {/* Store Icon */}
                  <div className="flex flex-col items-center gap-1">
                    <div className={cn(
                      "flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center",
                      seller.id === itemOptionsData?.sellerId
                        ? "bg-primary/20"
                        : "bg-gray-100"
                    )}>
                      <Store
                        size={20}
                        className={cn(
                          seller.id === itemOptionsData?.sellerId ? "text-primary" : "text-gray-600"
                        )}
                      />
                    </div>

                    {/* Status Badge */}
                    <div className={cn(
                      "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                      seller.enabled
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    )}>
                      <div className={cn(
                        "w-1.5 h-1.5 rounded-full",
                        seller.enabled ? "bg-green-500" : "bg-red-500"
                      )}></div>
                      {seller.enabled ? "Open" : "Closed"}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    {/* Outlet Name & Status */}
                    <div className="flex-1 min-w-0">
                      <h3 className={cn(
                        "font-semibold text-lg leading-tight truncate",
                        seller.id === itemOptionsData?.sellerId ? "text-primary" : "text-gray-900"
                      )}>
                        {seller.name}
                      </h3>
                      {seller.businessName && seller.businessName !== seller.name && (
                        <p className="text-sm text-gray-500 truncate mt-0.5">
                          {seller.businessName}
                        </p>
                      )}
                    </div>

                    {/* Address */}
                    <div className="flex items-center gap-1 py-1">
                      <MapPin size={14} className="text-gray-400 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-gray-600 line-clamp-2 leading-4">
                        {seller.address}
                      </p>
                    </div>

                    {/* Distance Info */}
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1.5 p-1 bg-gray-100 rounded-lg">
                        <Clock size={12} className="text-gray-500" />
                        <span className="text-xs font-medium text-gray-700">
                          {seller.distanceInKm.toFixed(1)} km
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        • {Math.ceil(seller.distanceInKm * 4)} mins away
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </BottomSheet>

      {/* MoQ Popup */}
      <MoQPopup
        visible={showMoq || showMov}
        onClose={() => {
          setShowMoq(false);
          setShowMov(false);
        }}
        qty={itemOptionsData?.minOrderQty || 0}
        currentQty={Object.values(cart).reduce((acc, item) => {
          const itemDetails = itemMap.get(item.itemId);
          if (itemDetails && itemDetails.unitWtFactor) {
            return acc + item.qty * itemDetails.unitWtFactor;
          }
          return acc + item.qty;
        }, 0)}
        value={itemOptionsData?.minOrderValue || 0}
        showMoq={showMoq}
        showMov={showMov}
      />
      {showToast && (
        <Toast
          message={toastMessage}
          type={toastType}
          duration={2500}
          onClose={() => setShowToast(false)}
          position="bottom-center"
          showIcon
          showCloseButton={false}
          width="full"
          autoClose={true}
        />
      )}
      {errorMessage && showErrorToast && (
        <Toast
          message={errorMessage}
          type={"error"}
          duration={5000}
          onClose={() => setShowErrorToast(false)}
          showIcon
          position="bottom-center"
          showCloseButton={false}
          width="full"
          autoClose={true}
        />
      )}

      <div hidden>
        <DotLottieReact
          src="https://lottie.host/620648dc-991d-4fb4-8109-cae58c6ce5ed/Evd5o9rjUY.lottie"
          loop={false}
          autoplay={false}
        />
        <DotLottieReact
          src="https://lottie.host/41dff85f-f65a-4e37-9f95-96722cc8c865/78ctiXySbm.lottie"
          loop={false}
          autoplay={false}
        />
      </div>
    </div>
  );
};

export default ChooseItems;

//triggers when there is an uncaught error thrown in the loader, action or component
export function ErrorBoundary() {
  const navigate = useNavigate();
  const error = useRouteError();

  const { appSource, networkConfig } = useAppConfigStore((state) => state);

  const handleClose = () => {
    if (appSource === "whatsappchat") {
      handleWhatsappClick(networkConfig?.wabMobileNumber || "");
    } else {
      navigate("/home/<USER>");
    }
  };

  if (
    isRouteErrorResponse(error) &&
    error.statusText === "NO_ITEM" &&
    appSource === "whatsappchat"
  ) {
    return (
      <InfoModel
        title="Sorry, we're closed!"
        message="We're currently not accepting orders. Please check back later."
        buttonType="primary"
        buttonText="Go Back"
        specialCase="BookingClosed"
        onRedirect={handleClose}
        isCountdownRedirectionAllowed={false}
      />
    );
  }

  if (isRouteErrorResponse(error) && error.statusText === "NO_ITEM") {
    return (
      <InfoModel
        title="Sorry, we're closed!"
        message="We're currently not accepting orders. Please check back later."
        buttonType="primary"
        buttonText="Go Back"
        specialCase="BookingClosed"
        onRedirect={handleClose}
        isCountdownRedirectionAllowed={true}
      />
    );
  }

  return (
    <InfoModel
      title="Sorry!"
      message="Something went wrong. We will be back soon."
      buttonType="primary"
      buttonText="Go Back"
      onRedirect={handleClose}
      countdownStart={60}
      isCountdownRedirectionAllowed={false}
    />
  );

  // return <ErrorBoundaryComponent onClose={handleClose} />;
}
